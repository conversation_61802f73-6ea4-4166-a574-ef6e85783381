"""
Master Autonomous Controller
Integrates all autonomous trading components into a unified system
"""

import asyncio
from datetime import datetime, time
import logging
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

class AutonomousController:
    """
    Master controller for fully autonomous trading
    """
    
    def __init__(self, exchange, config: Dict):
        self.exchange = exchange
        self.config = config

        # Initialize core components
        self.portfolio_manager = None
        self.performance_tracker = None
        self.trade_executor = None
        self.ml_manager = None
        self.adaptive_updater = None
        self.rl_agent = None
        self.sentiment_manager = None
        
        # Control flags
        self.is_running = False
        self.use_rl = config.get('use_rl', False)
        self.trading_hours = config.get('trading_hours', {'start': 0, 'end': 24})  # 24/7 by default
        
        # Performance monitoring
        self.cycle_count = 0
        self.last_performance_check = datetime.now()
        
        logger.info("[AUTONOMOUS] Controller initialized")
    
    async def initialize(self):
        """Initialize all components"""
        logger.info("[AUTONOMOUS] Initializing autonomous trading controller...")
        
        # Initialize portfolio manager
        from portfolio.portfolio_manager import PortfolioManager
        self.portfolio_manager = PortfolioManager(
            initial_balance=self.config.get('initial_balance', 1000.0),
            max_positions=self.config.get('max_positions', 5)
        )
        
        # Initialize performance tracker
        from monitoring.performance_tracker import PerformanceTracker
        self.performance_tracker = PerformanceTracker()
        
        # Initialize trade executor
        from execution.autonomous_executor import AutonomousTradeExecutor
        from core.adaptive_risk import AdaptiveRiskManager
        self.trade_executor = AutonomousTradeExecutor(
            exchange=self.exchange,
            risk_manager=AdaptiveRiskManager(),
            min_confidence=self.config.get('min_confidence', 0.65)
        )
        
        # Initialize ML components
        try:
            from ml.models import MLModelManager
            self.ml_manager = MLModelManager()
            
            from ml.adaptive_updater import AdaptiveModelUpdater
            self.adaptive_updater = AdaptiveModelUpdater(
                self.performance_tracker,
                self.ml_manager
            )
        except Exception as e:
            logger.warning(f"[AUTONOMOUS] ML components not available: {e}")
        
        # Initialize sentiment integration
        try:
            from core.sentiment_integration_manager import SentimentIntegrationManager
            self.sentiment_manager = SentimentIntegrationManager(
                update_interval=self.config.get('sentiment_update_interval', 300)  # 5 minutes default
            )
            self.sentiment_manager.start_sentiment_monitoring()
            logger.info("✅ [AUTONOMOUS] Sentiment integration initialized")
        except Exception as e:
            logger.warning(f"[AUTONOMOUS] Sentiment integration not available: {e}")

        # Initialize RL agent if enabled
        if self.use_rl:
            try:
                from data.exchange import ExchangeDataFetcher
                from ml.trading_env import TradingEnvironment
                from ml.rl_agent import TradingRLAgent

                data_fetcher = ExchangeDataFetcher(exchange_id='htx')
                trading_env = TradingEnvironment(data_fetcher)

                self.rl_agent = TradingRLAgent(trading_env)
                if not self.rl_agent.load_model():
                    logger.info("[AUTONOMOUS] Training new RL agent...")
                    await self.rl_agent.train(total_timesteps=50000)
            except Exception as e:
                logger.warning(f"[AUTONOMOUS] RL agent not available: {e}")
                self.use_rl = False

        logger.info("[AUTONOMOUS] Initialization complete")

    async def _enhance_decision_with_sentiment(self, decision: str, symbol: str, parsed_response: Dict) -> Dict:
        """Enhance trading decision with sentiment analysis"""
        try:
            if not self.sentiment_manager:
                # Return original decision if sentiment not available
                return {
                    'decision': decision,
                    'confidence': parsed_response.get('confidence', 50),
                    'explanation': parsed_response.get('explanation', ''),
                    'leverage_position_sizing': parsed_response.get('leverage_position_sizing', {})
                }

            # Get sentiment for the symbol
            sentiment_signal = self.sentiment_manager.get_sentiment_for_symbol(symbol)

            if not sentiment_signal:
                logger.warning(f"[AUTONOMOUS] No sentiment data available for {symbol}")
                return {
                    'decision': decision,
                    'confidence': parsed_response.get('confidence', 50),
                    'explanation': parsed_response.get('explanation', ''),
                    'leverage_position_sizing': parsed_response.get('leverage_position_sizing', {})
                }

            # Enhance decision based on sentiment
            original_confidence = parsed_response.get('confidence', 50) / 100.0
            sentiment_confidence = sentiment_signal.confidence
            sentiment_value = sentiment_signal.overall_sentiment

            # Calculate sentiment-adjusted confidence
            if decision == 'BUY' and sentiment_value > 0:
                # Bullish sentiment supports buy decision
                confidence_boost = min(0.2, sentiment_value * sentiment_confidence * 0.3)
                adjusted_confidence = min(0.95, original_confidence + confidence_boost)
            elif decision == 'SELL' and sentiment_value < 0:
                # Bearish sentiment supports sell decision
                confidence_boost = min(0.2, abs(sentiment_value) * sentiment_confidence * 0.3)
                adjusted_confidence = min(0.95, original_confidence + confidence_boost)
            elif (decision == 'BUY' and sentiment_value < -0.3) or (decision == 'SELL' and sentiment_value > 0.3):
                # Strong opposing sentiment - reduce confidence
                confidence_penalty = min(0.3, abs(sentiment_value) * sentiment_confidence * 0.4)
                adjusted_confidence = max(0.1, original_confidence - confidence_penalty)

                # Consider changing decision if sentiment is very strong and opposing
                if abs(sentiment_value) > 0.5 and sentiment_confidence > 0.8:
                    logger.warning(f"[AUTONOMOUS] Strong opposing sentiment for {symbol}: {sentiment_value:.2f}")
                    if adjusted_confidence < 0.3:
                        decision = 'WAIT'
                        adjusted_confidence = 0.5
            else:
                # Neutral or weak sentiment - slight adjustment
                adjusted_confidence = original_confidence

            # Adjust position sizing based on sentiment
            leverage_sizing = parsed_response.get('leverage_position_sizing', {}).copy()
            if leverage_sizing and sentiment_signal:
                # Reduce position size if sentiment uncertainty is high
                if sentiment_confidence < 0.5:
                    size_reduction = (0.5 - sentiment_confidence) * 0.5  # Max 25% reduction
                    if 'position_size_usd' in leverage_sizing:
                        leverage_sizing['position_size_usd'] *= (1 - size_reduction)
                    if 'position_size_percent' in leverage_sizing:
                        leverage_sizing['position_size_percent'] *= (1 - size_reduction)

                # Add sentiment risk adjustment
                leverage_sizing['sentiment_risk_adjustment'] = {
                    'sentiment_value': sentiment_value,
                    'sentiment_confidence': sentiment_confidence,
                    'risk_adjustment_factor': 1 - (abs(sentiment_value) * (1 - sentiment_confidence) * 0.2)
                }

            # Enhanced explanation
            enhanced_explanation = parsed_response.get('explanation', '')
            enhanced_explanation += f"\n\n📊 SENTIMENT ANALYSIS:\n"
            enhanced_explanation += f"Overall Sentiment: {sentiment_value:.2f} ({sentiment_signal.confidence:.1%} confidence)\n"
            enhanced_explanation += f"News Impact: {sentiment_signal.news_impact:.2f}\n"
            enhanced_explanation += f"Social Buzz: {sentiment_signal.social_buzz:.2f}\n"
            enhanced_explanation += f"Fear/Greed Index: {sentiment_signal.fear_greed_index:.2f}\n"
            enhanced_explanation += f"Sentiment Reasoning: {sentiment_signal.reasoning}\n"
            enhanced_explanation += f"Confidence Adjustment: {original_confidence:.1%} → {adjusted_confidence:.1%}"

            return {
                'decision': decision,
                'confidence': int(adjusted_confidence * 100),
                'explanation': enhanced_explanation,
                'leverage_position_sizing': leverage_sizing,
                'sentiment_data': {
                    'overall_sentiment': sentiment_value,
                    'confidence': sentiment_confidence,
                    'news_impact': sentiment_signal.news_impact,
                    'social_buzz': sentiment_signal.social_buzz,
                    'fear_greed_index': sentiment_signal.fear_greed_index,
                    'reasoning': sentiment_signal.reasoning,
                    'original_confidence': original_confidence,
                    'adjusted_confidence': adjusted_confidence
                }
            }

        except Exception as e:
            logger.error(f"[AUTONOMOUS] Error enhancing decision with sentiment: {e}")
            return {
                'decision': decision,
                'confidence': parsed_response.get('confidence', 50),
                'explanation': parsed_response.get('explanation', ''),
                'leverage_position_sizing': parsed_response.get('leverage_position_sizing', {})
            }

    async def run_autonomous_trading(self):
        """Main autonomous trading loop"""
        logger.info("[AUTONOMOUS] Starting fully autonomous trading system...")
        self.is_running = True
        
        try:
            while self.is_running:
                cycle_start = datetime.now()
                
                # Check trading hours
                if not self.is_trading_hours():
                    await asyncio.sleep(300)  # Wait 5 minutes
                    continue
                
                # Check if we need to update models
                if self.adaptive_updater:
                    await self.check_model_updates()
                
                # Get market data and make trading decision
                decision_data = await self.make_trading_decision()
                
                # Portfolio risk management
                if self.portfolio_manager:
                    await self.portfolio_manager.rebalance_portfolio()
                
                # Execute trading decision
                if decision_data['decision'] != 'WAIT':
                    execution_result = await self.execute_trade(decision_data)
                    await self.record_trade_result(execution_result, decision_data)
                
                # Performance monitoring
                await self.monitor_performance()
                
                # Update cycle count
                self.cycle_count += 1
                
                # Display status
                await self.display_status()
                
                # Adaptive sleep based on market activity
                sleep_time = self.calculate_adaptive_sleep_time()
                await asyncio.sleep(sleep_time)
                
        except Exception as e:
            logger.error(f"[AUTONOMOUS] Critical error in trading loop: {e}")
            await self.emergency_shutdown()
        finally:
            self.is_running = False
    
    async def make_trading_decision(self) -> Dict:
        """Make autonomous trading decision"""
        if self.use_rl and self.rl_agent:
            # Use RL agent for decision making
            return await self.make_rl_decision()
        else:
            # Use traditional ML ensemble approach
            return await self.make_ensemble_decision()
    
    async def make_rl_decision(self) -> Dict:
        """Make decision using RL agent"""
        try:
            # Get market observation
            market_obs = await self.get_market_observation()
            
            # Get RL action
            rl_action = self.rl_agent.predict(market_obs)
            
            # Convert to trading decision format
            direction = int(rl_action[0])
            position_size = float(rl_action[1])
            leverage = float(rl_action[2])
            
            decision_map = {0: 'WAIT', 1: 'LONG', 2: 'SHORT'}
            
            return {
                'decision': decision_map[direction],
                'confidence': min(95, 60 + position_size * 35),
                'position_size': position_size,
                'leverage': leverage,
                'source': 'rl_agent'
            }
        except Exception as e:
            logger.error(f"[AUTONOMOUS] RL decision error: {e}")
            return {'decision': 'WAIT', 'confidence': 0, 'source': 'error'}
    
    async def make_ensemble_decision(self) -> Dict:
        """Make decision using traditional ensemble approach with sentiment enhancement"""
        try:
            # This would integrate with your existing run_trading_system_with_dynamic_selection
            from main import run_trading_system_with_dynamic_selection

            decision, explanation, parsed_response, selected_symbol = run_trading_system_with_dynamic_selection(
                use_live_data=True,
                enable_dynamic_selection=True
            )

            # Enhance decision with sentiment analysis
            enhanced_decision = await self._enhance_decision_with_sentiment(
                decision, selected_symbol, parsed_response
            )

            return {
                'decision': enhanced_decision['decision'],
                'confidence': enhanced_decision['confidence'],
                'selected_symbol': selected_symbol,
                'leverage_position_sizing': enhanced_decision.get('leverage_position_sizing', {}),
                'explanation': enhanced_decision['explanation'],
                'sentiment_data': enhanced_decision.get('sentiment_data', {}),
                'source': 'ensemble_with_sentiment'
            }
        except Exception as e:
            logger.error(f"[AUTONOMOUS] Ensemble decision error: {e}")
            return {'decision': 'WAIT', 'confidence': 0, 'source': 'error'}
    
    async def execute_trade(self, decision_data: Dict) -> Dict:
        """Execute trading decision through portfolio manager"""
        try:
            # Execute through trade executor
            execution_result = await self.trade_executor.execute_trading_decision(decision_data)
            
            # If successful, update portfolio manager
            if execution_result['status'] == 'FILLED' and self.portfolio_manager:
                await self.portfolio_manager.open_position(
                    symbol=execution_result['symbol'],
                    side=execution_result['side'],
                    size=execution_result['amount'],
                    entry_price=execution_result['price'],
                    leverage=decision_data.get('leverage', 1.0)
                )
            
            return execution_result
        except Exception as e:
            logger.error(f"[AUTONOMOUS] Trade execution error: {e}")
            return {'status': 'ERROR', 'reason': str(e)}
    
    async def record_trade_result(self, execution_result: Dict, decision_data: Dict):
        """Record trade result for performance tracking"""
        if self.performance_tracker and execution_result['status'] == 'FILLED':
            trade_data = {
                'timestamp': datetime.now(),
                'symbol': execution_result['symbol'],
                'decision': decision_data['decision'],
                'confidence': decision_data.get('confidence', 0),
                'entry_price': execution_result['price'],
                'position_size': execution_result['amount'],
                'leverage': decision_data.get('leverage', 1.0),
                'execution_status': execution_result['status'],
                'trade_source': decision_data.get('source', 'autonomous')
            }
            self.performance_tracker.record_trade(trade_data)
    
    async def monitor_performance(self):
        """Monitor and log performance metrics"""
        if self.performance_tracker:
            # Calculate daily metrics every hour
            if (datetime.now() - self.last_performance_check).total_seconds() > 3600:
                metrics = self.performance_tracker.calculate_daily_metrics()
                logger.info(f"[PERFORMANCE] Daily metrics: {metrics}")
                self.last_performance_check = datetime.now()
    
    async def check_model_updates(self):
        """Check if models need updating"""
        if self.adaptive_updater:
            try:
                # Get recent market data for model updates
                # This would need to be implemented based on your data sources
                current_data = await self.get_recent_market_data()
                await self.adaptive_updater.check_and_update_models(current_data)
            except Exception as e:
                logger.error(f"[AUTONOMOUS] Model update check failed: {e}")
    
    async def get_market_observation(self):
        """Get current market observation for RL agent"""
        # This would return market features for the RL agent
        # For now, return dummy observation
        return np.random.random(50).astype(np.float32)
    
    async def get_recent_market_data(self):
        """Get recent market data for model updates"""
        # This would fetch recent market data
        # For now, return dummy data
        import pandas as pd
        return pd.DataFrame()
    
    def is_trading_hours(self) -> bool:
        """Check if current time is within trading hours"""
        current_hour = datetime.now().hour
        start_hour = self.trading_hours['start']
        end_hour = self.trading_hours['end']
        
        if start_hour <= end_hour:
            return start_hour <= current_hour < end_hour
        else:  # Overnight trading
            return current_hour >= start_hour or current_hour < end_hour
    
    def calculate_adaptive_sleep_time(self) -> float:
        """Calculate adaptive sleep time based on market conditions"""
        base_sleep = self.config.get('base_delay', 60)
        
        # Adjust based on portfolio activity
        if self.portfolio_manager and len(self.portfolio_manager.positions) > 0:
            return base_sleep * 0.5  # More frequent checks with open positions
        
        return base_sleep
    
    async def display_status(self):
        """Display current autonomous trading status"""
        if self.cycle_count % 10 == 0:  # Display every 10 cycles
            portfolio_summary = self.portfolio_manager.get_portfolio_summary() if self.portfolio_manager else {}
            
            logger.info(f"[STATUS] Cycle #{self.cycle_count}")
            logger.info(f"[STATUS] Balance: ${portfolio_summary.get('balance', 0):.2f}")
            logger.info(f"[STATUS] Open Positions: {portfolio_summary.get('open_positions', 0)}")
            logger.info(f"[STATUS] Portfolio Risk: {portfolio_summary.get('portfolio_risk', 0):.1%}")
    
    async def emergency_shutdown(self):
        """Emergency shutdown procedure"""
        logger.critical("[AUTONOMOUS] Emergency shutdown initiated")
        
        # Close all positions
        if self.portfolio_manager:
            for symbol in list(self.portfolio_manager.positions.keys()):
                try:
                    current_price = self.portfolio_manager.positions[symbol].current_price
                    await self.portfolio_manager.close_position(symbol, current_price)
                    logger.info(f"[EMERGENCY] Closed position: {symbol}")
                except Exception as e:
                    logger.error(f"[EMERGENCY] Failed to close {symbol}: {e}")
        
        self.is_running = False
        logger.critical("[AUTONOMOUS] Emergency shutdown complete")
    
    def stop(self):
        """Gracefully stop the autonomous controller"""
        logger.info("[AUTONOMOUS] Stopping autonomous trading...")
        self.is_running = False

"""
GUI Integration Module for EPINNOX v6
Integrates trading system with GUI components
"""

import logging
from typing import Dict, Any, Optional, Callable
from datetime import datetime
import threading
import time

logger = logging.getLogger(__name__)

try:
    from PyQt5.QtCore import QObject, pyqtSignal, QTimer
    from PyQt5.QtWidgets import QApplication
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False
    # Mock classes for when PyQt5 is not available
    class QObject:
        pass
    
    def pyqtSignal(*args):
        def decorator(func):
            return func
        return decorator

class TradingSystemGUIIntegration(QObject if PYQT_AVAILABLE else object):
    """
    Integration layer between trading system and GUI
    Handles data flow and event management
    """
    
    # Signals for GUI updates (only work if PyQt5 is available)
    if PYQT_AVAILABLE:
        data_updated = pyqtSignal(dict)
        trade_executed = pyqtSignal(dict)
        error_occurred = pyqtSignal(str)
        status_changed = pyqtSignal(str)
    
    def __init__(self, trading_system=None):
        if PYQT_AVAILABLE:
            super().__init__()
        
        self.trading_system = trading_system
        self.gui_components = {}
        self.data_cache = {}
        self.update_callbacks = []
        self.is_running = False
        self.update_thread = None
        self.update_interval = 5.0  # seconds
        
        logger.info("GUI Integration initialized")
    
    def register_gui_component(self, name: str, component: Any):
        """Register a GUI component for updates"""
        self.gui_components[name] = component
        logger.info(f"Registered GUI component: {name}")
    
    def unregister_gui_component(self, name: str):
        """Unregister a GUI component"""
        if name in self.gui_components:
            del self.gui_components[name]
            logger.info(f"Unregistered GUI component: {name}")
    
    def add_update_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Add a callback function for data updates"""
        self.update_callbacks.append(callback)
    
    def remove_update_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Remove a callback function"""
        if callback in self.update_callbacks:
            self.update_callbacks.remove(callback)
    
    def start_updates(self, interval: float = 5.0):
        """Start automatic GUI updates"""
        self.update_interval = interval
        self.is_running = True
        
        if not self.update_thread or not self.update_thread.is_alive():
            self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
            self.update_thread.start()
            logger.info(f"Started GUI updates with {interval}s interval")
    
    def stop_updates(self):
        """Stop automatic GUI updates"""
        self.is_running = False
        if self.update_thread:
            self.update_thread.join(timeout=1.0)
        logger.info("Stopped GUI updates")
    
    def _update_loop(self):
        """Main update loop running in separate thread"""
        while self.is_running:
            try:
                self.update_gui_data()
                time.sleep(self.update_interval)
            except Exception as e:
                logger.error(f"Error in GUI update loop: {e}")
                time.sleep(1.0)  # Brief pause before retrying
    
    def update_gui_data(self):
        """Update GUI with latest data"""
        try:
            # Get data from trading system
            if self.trading_system:
                data = self._collect_trading_data()
            else:
                data = self._get_mock_data()
            
            # Update cache
            self.data_cache.update(data)
            
            # Emit signals if PyQt5 is available
            if PYQT_AVAILABLE:
                self.data_updated.emit(data)
            
            # Call registered callbacks
            for callback in self.update_callbacks:
                try:
                    callback(data)
                except Exception as e:
                    logger.error(f"Error in update callback: {e}")
            
            # Update registered GUI components
            for name, component in self.gui_components.items():
                try:
                    if hasattr(component, 'update_data'):
                        component.update_data(data)
                except Exception as e:
                    logger.error(f"Error updating GUI component {name}: {e}")
        
        except Exception as e:
            logger.error(f"Error updating GUI data: {e}")
            if PYQT_AVAILABLE:
                self.error_occurred.emit(str(e))
    
    def _collect_trading_data(self) -> Dict[str, Any]:
        """Collect data from trading system"""
        try:
            data = {
                "timestamp": datetime.now().isoformat(),
                "status": "ACTIVE"
            }
            
            # Get portfolio data
            if hasattr(self.trading_system, 'portfolio'):
                portfolio = self.trading_system.portfolio
                data.update({
                    "balance": getattr(portfolio, 'balance', 0.0),
                    "total_pnl": getattr(portfolio, 'total_pnl', 0.0),
                    "active_positions": len(getattr(portfolio, 'positions', [])),
                })
            
            # Get performance data
            if hasattr(self.trading_system, 'performance_tracker'):
                perf = self.trading_system.performance_tracker
                data.update({
                    "total_trades": getattr(perf, 'total_trades', 0),
                    "win_rate": getattr(perf, 'win_rate', 0.0),
                    "sharpe_ratio": getattr(perf, 'sharpe_ratio', 0.0),
                })
            
            # Get current market data
            if hasattr(self.trading_system, 'current_data'):
                market_data = self.trading_system.current_data
                if market_data:
                    data.update({
                        "current_price": market_data.get('current_price', 0.0),
                        "price_change": market_data.get('price_change_24h', 0.0),
                        "volume": market_data.get('volume', 0.0),
                    })

            # Get sentiment data from shared data manager
            sentiment_data = self._get_sentiment_data()
            if sentiment_data:
                data["sentiment"] = sentiment_data

            return data
            
        except Exception as e:
            logger.error(f"Error collecting trading data: {e}")
            return self._get_mock_data()

    def _get_sentiment_data(self) -> Optional[Dict[str, Any]]:
        """Get sentiment data from shared data manager or sentiment manager"""
        try:
            # Try to get from shared data manager first
            from data.shared_data_manager import SharedDataManager
            shared_manager = SharedDataManager()

            # Get sentiment summary
            sentiment_summary = shared_manager.get_sentiment_summary()
            if sentiment_summary:
                return {
                    "summary": sentiment_summary,
                    "source": "shared_data_manager"
                }

            # Fallback to direct sentiment manager access
            try:
                from core.sentiment_integration_manager import SentimentIntegrationManager
                sentiment_manager = SentimentIntegrationManager()
                sentiment_summary = sentiment_manager.get_sentiment_summary()

                if sentiment_summary:
                    return {
                        "summary": sentiment_summary,
                        "source": "sentiment_manager"
                    }
            except Exception as e:
                logger.debug(f"Could not access sentiment manager directly: {e}")

            return None

        except Exception as e:
            logger.error(f"Error getting sentiment data: {e}")
            return None

    def _get_mock_data(self) -> Dict[str, Any]:
        """Get mock data for testing"""
        import random

        # Generate mock sentiment data
        mock_sentiment = {
            "summary": {
                "total_symbols": 6,
                "avg_sentiment": random.uniform(-0.3, 0.3),
                "avg_confidence": random.uniform(0.5, 0.8),
                "bullish_count": random.randint(1, 4),
                "bearish_count": random.randint(1, 3),
                "neutral_count": random.randint(1, 2),
                "last_update": datetime.now()
            },
            "source": "mock"
        }

        return {
            "timestamp": datetime.now().isoformat(),
            "status": "MOCK",
            "balance": 10000.0 + random.uniform(-1000, 1000),
            "total_pnl": random.uniform(-500, 500),
            "active_positions": random.randint(0, 3),
            "total_trades": random.randint(50, 200),
            "win_rate": random.uniform(0.4, 0.8),
            "sharpe_ratio": random.uniform(-1.0, 2.0),
            "current_price": 50000 + random.uniform(-5000, 5000),
            "price_change": random.uniform(-5.0, 5.0),
            "volume": random.uniform(1000000, 10000000),
            "sentiment": mock_sentiment,
            "message": f"Mock update at {datetime.now().strftime('%H:%M:%S')}"
        }
    
    def handle_trade_execution(self, trade_data: Dict[str, Any]):
        """Handle trade execution event"""
        try:
            logger.info(f"Trade executed: {trade_data}")
            
            if PYQT_AVAILABLE:
                self.trade_executed.emit(trade_data)
            
            # Update GUI components
            for name, component in self.gui_components.items():
                if hasattr(component, 'handle_trade'):
                    component.handle_trade(trade_data)
        
        except Exception as e:
            logger.error(f"Error handling trade execution: {e}")
    
    def handle_error(self, error_message: str):
        """Handle error event"""
        logger.error(f"Trading system error: {error_message}")
        
        if PYQT_AVAILABLE:
            self.error_occurred.emit(error_message)
        
        # Notify GUI components
        for name, component in self.gui_components.items():
            if hasattr(component, 'handle_error'):
                component.handle_error(error_message)
    
    def set_status(self, status: str):
        """Set system status"""
        logger.info(f"Status changed: {status}")
        
        if PYQT_AVAILABLE:
            self.status_changed.emit(status)
        
        # Update status in cache
        self.data_cache["status"] = status
        
        # Notify GUI components
        for name, component in self.gui_components.items():
            if hasattr(component, 'set_status'):
                component.set_status(status)
    
    def get_cached_data(self) -> Dict[str, Any]:
        """Get cached data"""
        return self.data_cache.copy()
    
    def force_update(self):
        """Force immediate GUI update"""
        self.update_gui_data()
    
    def _capture_live_terminal_data(self) -> Dict[str, Any]:
        """Capture live terminal data for testing"""
        return {
            'market_data': {
                'symbol': 'BTC/USDT',
                'price': 50000.0,
                'volume': 1000.0
            },
            'ai_analysis': {
                'decision': 'LONG',
                'confidence': 85
            },
            'timeframe_analysis': {
                'overall_trend': 'BULLISH'
            },
            'market_regime': {
                'current_regime': 'TRENDING'
            }
        }

    def _update_data_file(self, data: Dict[str, Any]):
        """Update data file for testing"""
        import json
        try:
            with open('gui_data.json', 'w') as f:
                json.dump(data, f, indent=2)
            logger.info("GUI data file updated successfully")
        except Exception as e:
            logger.error(f"Error updating data file: {e}")

    def cleanup(self):
        """Cleanup resources"""
        self.stop_updates()
        self.gui_components.clear()
        self.update_callbacks.clear()
        logger.info("GUI Integration cleaned up")

# Global instance for easy access
_gui_integration = None

def get_gui_integration(trading_system=None) -> TradingSystemGUIIntegration:
    """Get global GUI integration instance"""
    global _gui_integration
    if _gui_integration is None:
        _gui_integration = TradingSystemGUIIntegration(trading_system)
    return _gui_integration

def cleanup_gui_integration():
    """Cleanup global GUI integration"""
    global _gui_integration
    if _gui_integration:
        _gui_integration.cleanup()
        _gui_integration = None

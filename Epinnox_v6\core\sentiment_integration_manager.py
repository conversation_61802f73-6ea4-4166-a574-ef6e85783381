"""
Sentiment Integration Manager for Epinnox v6
Integrates NLP sentiment analysis into autonomous trading pipeline
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import threading
import time

logger = logging.getLogger(__name__)

# Import NLP components
try:
    from nlp.sentiment_analyzer import SentimentAnalyzer, SentimentScore
    from nlp.news_scraper import NewsScraperManager, NewsSource
    from nlp.social_monitor import SocialMediaMonitor, SocialPlatform
    from nlp.market_sentiment import MarketSentimentAggregator
    from nlp.nlp_features import NLPFeatureExtractor, NLPFeatures
    NLP_AVAILABLE = True
except ImportError as e:
    logger.warning(f"NLP components not available: {e}")
    NLP_AVAILABLE = False
    
    # Mock classes for when NLP is not available
    @dataclass
    class NLPFeatures:
        sentiment_momentum: float = 0.5
        news_volume_score: float = 0.5
        social_buzz_score: float = 0.5
        fear_greed_index: float = 0.5
        keyword_intensity: float = 0.5
        influencer_sentiment: float = 0.5
        viral_potential: float = 0.5
        controversy_score: float = 0.5
        urgency_score: float = 0.5
        confidence_score: float = 0.5
        feature_timestamp: datetime = None
        symbol: str = ""
        data_quality_score: float = 0.5

@dataclass
class SentimentSignal:
    """Sentiment signal for trading decisions"""
    symbol: str
    overall_sentiment: float  # -1.0 to 1.0 (bearish to bullish)
    confidence: float  # 0.0 to 1.0
    sentiment_momentum: float  # Rate of sentiment change
    news_impact: float  # News sentiment impact
    social_buzz: float  # Social media sentiment
    fear_greed_index: float  # Market fear/greed indicator
    data_quality: float  # Quality of underlying data
    timestamp: datetime
    reasoning: str  # Human-readable explanation

class SentimentIntegrationManager:
    """
    Manages sentiment analysis integration for autonomous trading
    Aggregates sentiment from news, social media, and market data
    """
    
    def __init__(self, update_interval: int = 300):  # 5 minutes default
        self.update_interval = update_interval
        self.is_running = False
        self.update_thread = None
        
        # Sentiment cache
        self.sentiment_cache: Dict[str, SentimentSignal] = {}
        self.cache_lock = threading.Lock()
        
        # Initialize NLP components if available
        if NLP_AVAILABLE:
            self.sentiment_analyzer = SentimentAnalyzer()
            self.news_scraper = NewsScraperManager(
                sources=[NewsSource.COINDESK, NewsSource.COINTELEGRAPH],
                max_articles_per_source=10
            )
            self.social_monitor = SocialMediaMonitor(
                platforms=[SocialPlatform.REDDIT]
            )
            self.market_sentiment = MarketSentimentAggregator()
            self.nlp_extractor = NLPFeatureExtractor()
            logger.info("✅ NLP components initialized successfully")
        else:
            logger.warning("⚠️ NLP components not available - using mock sentiment")
            
    def start_sentiment_monitoring(self):
        """Start background sentiment monitoring"""
        if self.is_running:
            logger.warning("Sentiment monitoring already running")
            return
            
        self.is_running = True
        self.update_thread = threading.Thread(target=self._sentiment_update_loop, daemon=True)
        self.update_thread.start()
        logger.info(f"🔄 Started sentiment monitoring (update interval: {self.update_interval}s)")
    
    def stop_sentiment_monitoring(self):
        """Stop background sentiment monitoring"""
        self.is_running = False
        if self.update_thread:
            self.update_thread.join(timeout=5.0)
        logger.info("⏹️ Stopped sentiment monitoring")
    
    def _sentiment_update_loop(self):
        """Background loop for updating sentiment data"""
        while self.is_running:
            try:
                # Update sentiment for common crypto symbols
                symbols = ['BTC', 'ETH', 'DOGE', 'ADA', 'SOL', 'MATIC']
                
                for symbol in symbols:
                    if not self.is_running:
                        break
                    
                    sentiment_signal = asyncio.run(self._get_symbol_sentiment(symbol))
                    
                    with self.cache_lock:
                        self.sentiment_cache[symbol] = sentiment_signal
                
                logger.info(f"📊 Updated sentiment for {len(symbols)} symbols")
                
                # Sleep for update interval
                time.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"Error in sentiment update loop: {e}")
                time.sleep(60)  # Wait 1 minute before retrying
    
    async def _get_symbol_sentiment(self, symbol: str) -> SentimentSignal:
        """Get comprehensive sentiment for a symbol"""
        try:
            if not NLP_AVAILABLE:
                return self._get_mock_sentiment(symbol)
            
            # Gather sentiment data from all sources
            news_sentiment = await self._get_news_sentiment(symbol)
            social_sentiment = await self._get_social_sentiment(symbol)
            
            # Extract NLP features
            nlp_features = self.nlp_extractor.extract_features(
                symbol=symbol,
                sentiment_scores=news_sentiment.get('scores', []),
                news_articles=news_sentiment.get('articles', []),
                social_posts=social_sentiment.get('posts', [])
            )
            
            # Calculate overall sentiment
            overall_sentiment = self._calculate_overall_sentiment(
                news_sentiment, social_sentiment, nlp_features
            )
            
            # Calculate confidence based on data quality
            confidence = self._calculate_sentiment_confidence(
                news_sentiment, social_sentiment, nlp_features
            )
            
            # Generate reasoning
            reasoning = self._generate_sentiment_reasoning(
                symbol, overall_sentiment, confidence, nlp_features
            )
            
            return SentimentSignal(
                symbol=symbol,
                overall_sentiment=overall_sentiment,
                confidence=confidence,
                sentiment_momentum=nlp_features.sentiment_momentum,
                news_impact=news_sentiment.get('impact', 0.5),
                social_buzz=nlp_features.social_buzz_score,
                fear_greed_index=nlp_features.fear_greed_index,
                data_quality=nlp_features.data_quality_score,
                timestamp=datetime.now(),
                reasoning=reasoning
            )
            
        except Exception as e:
            logger.error(f"Error getting sentiment for {symbol}: {e}")
            return self._get_mock_sentiment(symbol)
    
    def _get_mock_sentiment(self, symbol: str) -> SentimentSignal:
        """Generate mock sentiment when NLP is not available"""
        import random
        
        # Generate realistic mock sentiment
        base_sentiment = random.uniform(-0.3, 0.3)  # Slight bias
        confidence = random.uniform(0.4, 0.8)
        
        return SentimentSignal(
            symbol=symbol,
            overall_sentiment=base_sentiment,
            confidence=confidence,
            sentiment_momentum=random.uniform(-0.2, 0.2),
            news_impact=random.uniform(0.3, 0.7),
            social_buzz=random.uniform(0.3, 0.7),
            fear_greed_index=random.uniform(0.3, 0.7),
            data_quality=0.5,  # Mock data quality
            timestamp=datetime.now(),
            reasoning=f"Mock sentiment for {symbol} (NLP not available)"
        )
    
    async def _get_news_sentiment(self, symbol: str) -> Dict[str, Any]:
        """Get news sentiment for symbol"""
        try:
            # Scrape recent news
            articles = await self.news_scraper.scrape_all_sources()
            
            # Filter for symbol-relevant articles
            relevant_articles = self.news_scraper.filter_articles_by_symbols(
                articles, [symbol, symbol.replace('USDT', '')]
            )
            
            # Analyze sentiment of relevant articles
            sentiment_scores = []
            for article in relevant_articles[:10]:  # Limit to recent articles
                text = f"{article.title} {article.summary}"
                score = await self.sentiment_analyzer.analyze_text(text)
                sentiment_scores.append(score)
            
            # Calculate news impact
            if sentiment_scores:
                avg_sentiment = sum(s.compound_score for s in sentiment_scores) / len(sentiment_scores)
                impact = min(len(sentiment_scores) / 10.0, 1.0)  # More articles = higher impact
            else:
                avg_sentiment = 0.0
                impact = 0.0
            
            return {
                'sentiment': avg_sentiment,
                'impact': impact,
                'articles': relevant_articles,
                'scores': sentiment_scores
            }
            
        except Exception as e:
            logger.error(f"Error getting news sentiment: {e}")
            return {'sentiment': 0.0, 'impact': 0.0, 'articles': [], 'scores': []}
    
    async def _get_social_sentiment(self, symbol: str) -> Dict[str, Any]:
        """Get social media sentiment for symbol"""
        try:
            # Monitor social media
            posts = await self.social_monitor.monitor_all_platforms([symbol])
            
            # Analyze sentiment of posts
            sentiment_scores = []
            for post in posts[:20]:  # Limit to recent posts
                score = await self.sentiment_analyzer.analyze_text(post.content)
                sentiment_scores.append(score)
            
            # Calculate social sentiment
            if sentiment_scores:
                avg_sentiment = sum(s.compound_score for s in sentiment_scores) / len(sentiment_scores)
                buzz = min(len(posts) / 50.0, 1.0)  # More posts = higher buzz
            else:
                avg_sentiment = 0.0
                buzz = 0.0
            
            return {
                'sentiment': avg_sentiment,
                'buzz': buzz,
                'posts': posts,
                'scores': sentiment_scores
            }
            
        except Exception as e:
            logger.error(f"Error getting social sentiment: {e}")
            return {'sentiment': 0.0, 'buzz': 0.0, 'posts': [], 'scores': []}
    
    def _calculate_overall_sentiment(self, news_data: Dict, social_data: Dict, nlp_features: NLPFeatures) -> float:
        """Calculate overall sentiment from all sources"""
        # Weight different sources
        news_weight = 0.4
        social_weight = 0.3
        features_weight = 0.3
        
        news_sentiment = news_data.get('sentiment', 0.0)
        social_sentiment = social_data.get('sentiment', 0.0)
        features_sentiment = (nlp_features.sentiment_momentum - 0.5) * 2  # Convert to -1 to 1 range
        
        overall = (
            news_sentiment * news_weight +
            social_sentiment * social_weight +
            features_sentiment * features_weight
        )
        
        # Clamp to valid range
        return max(-1.0, min(1.0, overall))
    
    def _calculate_sentiment_confidence(self, news_data: Dict, social_data: Dict, nlp_features: NLPFeatures) -> float:
        """Calculate confidence in sentiment analysis"""
        # Base confidence on data quality and volume
        news_confidence = min(news_data.get('impact', 0.0) * 2, 1.0)
        social_confidence = min(social_data.get('buzz', 0.0) * 2, 1.0)
        features_confidence = nlp_features.confidence_score
        
        # Average confidence with minimum threshold
        avg_confidence = (news_confidence + social_confidence + features_confidence) / 3
        return max(0.3, avg_confidence)  # Minimum 30% confidence
    
    def _generate_sentiment_reasoning(self, symbol: str, sentiment: float, confidence: float, features: NLPFeatures) -> str:
        """Generate human-readable sentiment reasoning"""
        sentiment_label = "BULLISH" if sentiment > 0.1 else "BEARISH" if sentiment < -0.1 else "NEUTRAL"
        confidence_label = "HIGH" if confidence > 0.7 else "MEDIUM" if confidence > 0.5 else "LOW"
        
        reasoning = f"{sentiment_label} sentiment for {symbol} with {confidence_label} confidence ({confidence:.1%}). "
        
        if features.news_volume_score > 0.6:
            reasoning += "High news volume detected. "
        if features.social_buzz_score > 0.6:
            reasoning += "Strong social media buzz. "
        if features.fear_greed_index > 0.7:
            reasoning += "Market showing greed signals. "
        elif features.fear_greed_index < 0.3:
            reasoning += "Market showing fear signals. "
        
        return reasoning.strip()
    
    def get_sentiment_for_symbol(self, symbol: str) -> Optional[SentimentSignal]:
        """Get cached sentiment for a symbol"""
        with self.cache_lock:
            return self.sentiment_cache.get(symbol.replace('/USDT', '').replace('USDT', ''))
    
    def get_all_sentiment_data(self) -> Dict[str, SentimentSignal]:
        """Get all cached sentiment data"""
        with self.cache_lock:
            return self.sentiment_cache.copy()
    
    def get_sentiment_summary(self) -> Dict[str, Any]:
        """Get summary of current sentiment state"""
        with self.cache_lock:
            if not self.sentiment_cache:
                return {
                    'total_symbols': 0,
                    'avg_sentiment': 0.0,
                    'avg_confidence': 0.0,
                    'bullish_count': 0,
                    'bearish_count': 0,
                    'neutral_count': 0
                }
            
            sentiments = list(self.sentiment_cache.values())
            
            avg_sentiment = sum(s.overall_sentiment for s in sentiments) / len(sentiments)
            avg_confidence = sum(s.confidence for s in sentiments) / len(sentiments)
            
            bullish_count = sum(1 for s in sentiments if s.overall_sentiment > 0.1)
            bearish_count = sum(1 for s in sentiments if s.overall_sentiment < -0.1)
            neutral_count = len(sentiments) - bullish_count - bearish_count
            
            return {
                'total_symbols': len(sentiments),
                'avg_sentiment': avg_sentiment,
                'avg_confidence': avg_confidence,
                'bullish_count': bullish_count,
                'bearish_count': bearish_count,
                'neutral_count': neutral_count,
                'last_update': max(s.timestamp for s in sentiments) if sentiments else None
            }

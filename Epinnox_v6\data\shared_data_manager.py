"""
Shared Data Manager
Provides centralized data fetching and caching for all bot instances
"""
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
import time
import threading
import random
from datetime import datetime, timedelta
from collections import defaultdict

# Import exchange data fetcher
from data.exchange import ExchangeDataFetcher

class SharedDataManager(QObject):
    """
    Centralized data manager that coordinates data fetching across all bot instances.
    Implements caching and rate limiting to prevent excessive API calls.
    """
    # Signals
    data_updated = pyqtSignal(dict)

    # Singleton instance
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(SharedDataManager, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        if self._initialized:
            return

        super().__init__()
        self._initialized = True

        # Initialize data fetcher
        self.data_fetcher = None
        try:
            self.data_fetcher = ExchangeDataFetcher(exchange_id='htx')
            print("Initialized exchange data fetcher")
        except Exception as e:
            print(f"Error initializing exchange data fetcher: {e}")

        # Initialize data cache
        self.cache = {
            'spot_data': {},
            'futures_data': {},
            'combined_data': {},
            'features': {},
            'sentiment_data': {},  # Add sentiment data cache
            'last_update': defaultdict(lambda: 0)
        }

        # Initialize sentiment manager
        self.sentiment_manager = None
        try:
            from core.sentiment_integration_manager import SentimentIntegrationManager
            self.sentiment_manager = SentimentIntegrationManager(update_interval=300)
            self.sentiment_manager.start_sentiment_monitoring()
            print("✅ Sentiment integration initialized in SharedDataManager")
        except Exception as e:
            print(f"⚠️ Sentiment integration not available: {e}")

        # Rate limiting settings
        self.min_fetch_interval = 5  # Minimum seconds between fetches for the same symbol

        # Start update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_cache)
        self.update_timer.start(5000)  # Update cache every 5 seconds

        # Active symbols being monitored
        self.active_symbols = set()

        print("Shared Data Manager initialized")

    def register_symbol(self, symbol):
        """Register a symbol to be monitored"""
        self.active_symbols.add(symbol)

    def unregister_symbol(self, symbol):
        """Unregister a symbol from monitoring"""
        if symbol in self.active_symbols:
            self.active_symbols.remove(symbol)

    def update_cache(self):
        """Update the data cache for all active symbols"""
        if not self.active_symbols:
            return

        # Randomly select one symbol to update to avoid rate limiting
        if self.active_symbols:
            symbol = random.choice(list(self.active_symbols))
            self.fetch_data_for_symbol(symbol)

    def fetch_data_for_symbol(self, symbol):
        """Fetch data for a specific symbol"""
        # Check if we need to fetch new data (rate limiting)
        current_time = time.time()
        if current_time - self.cache['last_update'].get(symbol, 0) < self.min_fetch_interval:
            return

        # Update last fetch time
        self.cache['last_update'][symbol] = current_time

        # Fetch data
        if self.data_fetcher:
            try:
                # Prepare symbols
                futures_symbol = symbol if ':USDT' in symbol else f"{symbol}:USDT"
                spot_symbol = symbol.split(':')[0] if ':' in symbol else symbol

                # Fetch combined data
                combined_data = self.data_fetcher.fetch_combined_data(
                    spot_symbol=spot_symbol,
                    futures_symbol=futures_symbol,
                    timeframe='1m',
                    limit=100,
                    include_trades=True
                )

                # Extract features
                features = self.data_fetcher.extract_features_from_combined_data(combined_data)

                # Fetch sentiment data if available
                sentiment_data = None
                if self.sentiment_manager:
                    sentiment_signal = self.sentiment_manager.get_sentiment_for_symbol(symbol)
                    if sentiment_signal:
                        sentiment_data = {
                            'overall_sentiment': sentiment_signal.overall_sentiment,
                            'confidence': sentiment_signal.confidence,
                            'sentiment_momentum': sentiment_signal.sentiment_momentum,
                            'news_impact': sentiment_signal.news_impact,
                            'social_buzz': sentiment_signal.social_buzz,
                            'fear_greed_index': sentiment_signal.fear_greed_index,
                            'reasoning': sentiment_signal.reasoning,
                            'timestamp': sentiment_signal.timestamp.isoformat()
                        }

                # Update cache
                self.cache['spot_data'][spot_symbol] = combined_data.get('spot_ohlcv', [])
                self.cache['futures_data'][futures_symbol] = combined_data.get('futures_ohlcv', [])
                self.cache['combined_data'][symbol] = combined_data
                self.cache['features'][symbol] = features
                if sentiment_data:
                    self.cache['sentiment_data'][symbol] = sentiment_data

                # Emit data updated signal with sentiment
                update_data = {
                    'symbol': symbol,
                    'timestamp': datetime.now().isoformat(),
                    'data_type': 'combined_data'
                }
                if sentiment_data:
                    update_data['sentiment'] = sentiment_data

                self.data_updated.emit(update_data)

            except Exception as e:
                print(f"Error fetching data for {symbol}: {e}")
        else:
            print(f"Data fetcher not available for {symbol}. Please initialize the data fetcher.")

    def get_data(self, symbol, data_type='combined_data'):
        """Get data from cache for a specific symbol"""
        # Register symbol if not already registered
        if symbol not in self.active_symbols:
            self.register_symbol(symbol)

        # Check if we have data in cache
        if data_type == 'combined_data' and symbol in self.cache['combined_data']:
            return self.cache['combined_data'][symbol]
        elif data_type == 'features' and symbol in self.cache['features']:
            return self.cache['features'][symbol]
        elif data_type == 'sentiment_data' and symbol in self.cache['sentiment_data']:
            return self.cache['sentiment_data'][symbol]
        elif data_type == 'spot_data':
            spot_symbol = symbol.split(':')[0] if ':' in symbol else symbol
            if spot_symbol in self.cache['spot_data']:
                return self.cache['spot_data'][spot_symbol]
        elif data_type == 'futures_data':
            futures_symbol = symbol if ':USDT' in symbol else f"{symbol}:USDT"
            if futures_symbol in self.cache['futures_data']:
                return self.cache['futures_data'][futures_symbol]

        # If no data in cache, fetch it now
        self.fetch_data_for_symbol(symbol)

        # Return data from cache or empty dict if not available
        if data_type == 'combined_data':
            return self.cache['combined_data'].get(symbol, {})
        elif data_type == 'features':
            return self.cache['features'].get(symbol, {})
        elif data_type == 'sentiment_data':
            return self.cache['sentiment_data'].get(symbol, {})
        elif data_type == 'spot_data':
            spot_symbol = symbol.split(':')[0] if ':' in symbol else symbol
            return self.cache['spot_data'].get(spot_symbol, [])
        elif data_type == 'futures_data':
            futures_symbol = symbol if ':USDT' in symbol else f"{symbol}:USDT"
            return self.cache['futures_data'].get(futures_symbol, [])

        return {}

    def get_sentiment_summary(self):
        """Get overall sentiment summary from sentiment manager"""
        if self.sentiment_manager:
            return self.sentiment_manager.get_sentiment_summary()
        return {}

    # Mock data generation has been removed in favor of real data

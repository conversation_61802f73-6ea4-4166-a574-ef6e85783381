#!/usr/bin/env python3
"""
Epinnox v6 Trading System Launcher
Clean modular launcher using existing components
"""

import sys
import os
import logging
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_unicode_console():
    """Setup console for proper Unicode handling on Windows"""
    if sys.platform == 'win32':
        try:
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8', errors='replace')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8', errors='replace')
        except Exception as e:
            logger.warning(f"Could not set UTF-8 encoding: {e}")
    return True

# Setup Unicode console
setup_unicode_console()

# Import PyQt5
try:
    from PyQt5.QtWidgets import QApplication, QMessageBox
    from PyQt5.QtCore import Qt
    logger.info("✓ PyQt5 loaded successfully")
except ImportError:
    logger.error("✗ PyQt5 not installed. Install with: pip install PyQt5")
    sys.exit(1)

# Import existing modular components
try:
    from gui.main_window import TradingSystemGUI
    from trading.trading_system_interface import TradingSystemInterface
    from gui_integration import TradingSystemGUIIntegration
    logger.info("✓ Core modules imported successfully")
except ImportError as e:
    logger.error(f"✗ Failed to import core modules: {e}")
    # Try fallback imports
    try:
        from gui.main_window import TradingSystemGUI
        logger.info("✓ GUI module imported")
    except ImportError:
        logger.error("✗ GUI module not available")
        sys.exit(1)

def show_error_dialog(title, message):
    """Show error dialog to user"""
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.exec_()
    except Exception:
        print(f"ERROR: {title} - {message}")

def main():
    """Main application entry point"""
    try:
        logger.info("🚀 Starting Epinnox v6 Trading System...")
        
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("Epinnox v6")
        app.setApplicationVersion("6.0")
        
        # Enable high DPI scaling
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # Create main GUI window
        main_window = TradingSystemGUI()
        
        # Try to create trading system interface
        try:
            trading_system = TradingSystemInterface()
            main_window.set_trading_system(trading_system)
            logger.info("✓ Trading system interface connected")
        except Exception as e:
            logger.warning(f"Trading system interface not available: {e}")
        
        # Try to create GUI integration layer
        try:
            gui_integration = TradingSystemGUIIntegration(trading_system if 'trading_system' in locals() else None)
            main_window.set_gui_integration(gui_integration)
            logger.info("✓ GUI integration layer connected")
        except Exception as e:
            logger.warning(f"GUI integration not available: {e}")
        
        # Show main window
        main_window.show()
        
        logger.info("✅ Epinnox v6 launched successfully")
        
        # Start event loop
        return app.exec_()
        
    except Exception as e:
        error_msg = f"Failed to launch Epinnox v6: {e}"
        logger.error(f"❌ {error_msg}")
        show_error_dialog("Launch Error", error_msg)
        return 1

if __name__ == "__main__":
    sys.exit(main())
